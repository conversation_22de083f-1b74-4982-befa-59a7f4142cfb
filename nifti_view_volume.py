import nibabel as nib
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import matplotlib.patches as patches

class VolumeViewer:
    def __init__(self, volume_path):
        # Chargement du volume
        self.volume = nib.load(volume_path).get_fdata().astype(np.uint8)
        self.volume_path = volume_path
        self.current_slice = 0
        self.max_slice = self.volume.shape[0] - 1

        print(f"Volume chargé : {self.volume.shape} (Z, Y, X)")
        print(f"Valeurs uniques dans le volume : {np.unique(self.volume)}")

        # Détection automatique du type de données
        unique_values = np.unique(self.volume)
        self.is_mask = self._detect_mask(unique_values)

        if self.is_mask:
            print("Détection : Volume de masque/segmentation détecté")
            print("Application du colormap pour les classes 0-4")
            # Colormap pour les masques de segmentation
            self.cmap = ListedColormap([
                (0, 0, 0),        # 0: background - noir
                (0, 0, 1),        # 1: frontwall - bleu
                (0, 1, 0),        # 2: backwall - vert
                (1, 0, 0),        # 3: flaw - rouge
                (1, 1, 0)         # 4: indication - jaune
            ])
            self.vmin, self.vmax = 0, 4
        else:
            print("Détection : Volume d'intensité standard détecté")
            print("Application du colormap en niveaux de gris")
            self.cmap = 'gray'
            self.vmin, self.vmax = self.volume.min(), self.volume.max()

        # Configuration de la figure
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.fig.suptitle(f"Visualiseur de Volume - {volume_path.split('/')[-1]}", fontsize=14)

        # Affichage initial
        self.update_display()

        # Configuration des contrôles
        self.setup_controls()

        # Connexion des événements
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)

        plt.show()

    def _detect_mask(self, unique_values):
        """Détecte si le volume est un masque de segmentation"""
        # Critères pour détecter un masque :
        # 1. Valeurs entières uniquement
        # 2. Valeurs dans la plage 0-4 (ou sous-ensemble)
        # 3. Nombre limité de valeurs uniques (≤ 6)

        # Vérifier si toutes les valeurs sont des entiers
        if not np.allclose(unique_values, unique_values.astype(int)):
            return False

        # Vérifier si les valeurs sont dans la plage 0-4
        if unique_values.max() <= 4 and unique_values.min() >= 0:
            # Vérifier si on a un nombre raisonnable de classes
            if len(unique_values) <= 6:
                return True

        return False

    def setup_controls(self):
        """Configure les contrôles de l'interface"""
        # Ajouter des instructions
        instructions = [
            "CONTRÔLES:",
            "← → : Slice précédente/suivante",
            "Page Up/Down : +/-10 slices",
            "Home/End : Première/dernière slice",
            "Q ou Escape : Quitter",
            "S : Sauvegarder la slice actuelle",
            "I : Informations sur la slice"
        ]

        # Afficher les instructions dans le titre
        control_text = " | ".join(instructions[:3])
        self.ax.set_title(f"Slice {self.current_slice + 1}/{self.max_slice + 1} | {control_text}",
                         fontsize=10, pad=20)

    def update_display(self):
        """Met à jour l'affichage de la slice actuelle"""
        self.ax.clear()

        # Affichage de la slice
        current_data = self.volume[self.current_slice, :, :]

        im = self.ax.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)

        # Titre avec informations
        title = f"Slice {self.current_slice + 1}/{self.max_slice + 1}"
        if self.is_mask:
            unique_in_slice = np.unique(current_data)
            title += f" | Classes présentes: {unique_in_slice}"
        else:
            title += f" | Min: {current_data.min():.2f}, Max: {current_data.max():.2f}"

        self.ax.set_title(title, fontsize=10)
        self.ax.axis('off')

        # Ajouter une colorbar pour les masques
        if self.is_mask and not hasattr(self, 'colorbar'):
            self.colorbar = plt.colorbar(im, ax=self.ax, shrink=0.8)
            self.colorbar.set_label('Classes de segmentation')
            self.colorbar.set_ticks([0, 1, 2, 3, 4])
            self.colorbar.set_ticklabels(['Background', 'Frontwall', 'Backwall', 'Flaw', 'Indication'])

        # Instructions en bas
        instructions_text = "← → : Précédent/Suivant | PgUp/PgDn : ±10 | Home/End : Premier/Dernier | Q : Quitter | S : Sauvegarder | I : Info"
        self.fig.text(0.5, 0.02, instructions_text, ha='center', fontsize=9, style='italic')

        self.fig.canvas.draw()

    def on_key_press(self, event):
        """Gestion des événements clavier"""
        if event.key == 'right' or event.key == 'down':
            self.next_slice()
        elif event.key == 'left' or event.key == 'up':
            self.previous_slice()
        elif event.key == 'pagedown':
            self.jump_slices(10)
        elif event.key == 'pageup':
            self.jump_slices(-10)
        elif event.key == 'home':
            self.go_to_slice(0)
        elif event.key == 'end':
            self.go_to_slice(self.max_slice)
        elif event.key == 'q' or event.key == 'escape':
            plt.close(self.fig)
        elif event.key == 's':
            self.save_current_slice()
        elif event.key == 'i':
            self.show_slice_info()

    def next_slice(self):
        """Passe à la slice suivante"""
        if self.current_slice < self.max_slice:
            self.current_slice += 1
            self.update_display()
        else:
            print("Déjà à la dernière slice")

    def previous_slice(self):
        """Passe à la slice précédente"""
        if self.current_slice > 0:
            self.current_slice -= 1
            self.update_display()
        else:
            print("Déjà à la première slice")

    def jump_slices(self, n):
        """Saute de n slices"""
        new_slice = self.current_slice + n
        new_slice = max(0, min(new_slice, self.max_slice))
        if new_slice != self.current_slice:
            self.current_slice = new_slice
            self.update_display()

    def go_to_slice(self, slice_num):
        """Va directement à une slice spécifique"""
        slice_num = max(0, min(slice_num, self.max_slice))
        if slice_num != self.current_slice:
            self.current_slice = slice_num
            self.update_display()

    def save_current_slice(self):
        """Sauvegarde la slice actuelle comme image"""
        import os
        filename = f"slice_{self.current_slice:03d}.png"
        current_data = self.volume[self.current_slice, :, :]

        # Créer une figure temporaire pour la sauvegarde
        fig_save, ax_save = plt.subplots(figsize=(8, 6))
        ax_save.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        ax_save.set_title(f"Slice {self.current_slice + 1}/{self.max_slice + 1}")
        ax_save.axis('off')

        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig_save)

        print(f"Slice sauvegardée : {filename}")

    def show_slice_info(self):
        """Affiche des informations détaillées sur la slice actuelle"""
        current_data = self.volume[self.current_slice, :, :]
        unique_values, counts = np.unique(current_data, return_counts=True)

        print(f"\n=== INFORMATIONS SLICE {self.current_slice + 1} ===")
        print(f"Forme : {current_data.shape}")

        # Éviter les backslashes dans les f-strings
        type_text = "Masque de segmentation" if self.is_mask else "Volume d'intensité"
        print(f"Type : {type_text}")

        print(f"Min/Max : {current_data.min()} / {current_data.max()}")
        print(f"Valeurs uniques et leurs occurrences :")
        for val, count in zip(unique_values, counts):
            percentage = (count / current_data.size) * 100
            if self.is_mask:
                class_names = {0: 'Background', 1: 'Frontwall', 2: 'Backwall', 3: 'Flaw', 4: 'Indication'}
                class_name = class_names.get(int(val), f'Classe {int(val)}')
                print(f"  {class_name} ({int(val)}) : {count} pixels ({percentage:.1f}%)")
            else:
                print(f"  Valeur {val} : {count} pixels ({percentage:.1f}%)")
        print("=" * 40)


# === UTILISATION ===
if __name__ == "__main__":
    # Chemin vers le fichier
    path_to_seg = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent_3d_v3\case_001.nii.gz"

    # Lancement du visualiseur
    viewer = VolumeViewer(path_to_seg)
