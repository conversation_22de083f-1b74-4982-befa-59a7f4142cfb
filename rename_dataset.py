import os
from pathlib import Path
import re

# === CONFIGURATION ===
REMOVE_TEXT = ""                   # Wildcard à supprimer dans le nom
ADD_SUFFIX = ""                     # Suffixe à ajouter (avant l'extension)
ADD_PREFIX = ""                     # Préfixe à ajouter
ADD_SERIAL_SUFFIX = False           # Ajouter une sérialisation en suffixe ? (ex: _0001)
ADD_SERIAL_PREFIX = False           # Ajouter une sérialisation en préfixe ? (ex: 0001_)
SERIAL_REPLACE_ALL = True          # Remplacer complètement le nom par la sérialisation ?

# === NOUVELLES OPTIONS ===
REMOVE_DASHES = False               # Enlever tous les "-" du nom
REMOVE_UNDERSCORES = False          # Enlever tous les "_" du nom
PRESERVE_FIRST_N_CHARS = 0         # Préserver les N premiers caractères des modifications (0 = désactivé)
PRESERVE_LAST_N_CHARS = 0          # Préserver les N derniers caractères des modifications (0 = désactivé)
START_INDEX = 1                     # Indice de début pour appliquer les options (1-based)
END_INDEX = -1                      # Indice de fin pour appliquer les options (-1 = jusqu'à la fin)

# === Dossier cible ===
FOLDER_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset031_testTPI\imagesTr"

def wildcard_to_regex_fragment(wildcard):
    """
    Convertit un motif wildcard type '*_*_' en un fragment regex.
    Par exemple, '*_*_' devient '.*?_.*?_'
    """
    # Remplacer chaque * par .*? pour correspondance non-gourmande
    return wildcard.replace("*", ".*?")

def remove_wildcard_fragment_from_stem(stem, pattern):
    """
    Supprime la première correspondance du motif wildcard dans le nom.
    """
    regex_fragment = wildcard_to_regex_fragment(pattern)
    match = re.search(regex_fragment, stem)
    if match:
        return stem.replace(match.group(0), "", 1)  # Retire seulement la première occurrence
    return stem

def rename_files(folder_path):
    folder = Path(folder_path)
    files = sorted([f for f in folder.iterdir() if f.is_file()])

    # Calculer les indices réels
    total_files = len(files)
    start_idx = max(1, START_INDEX) - 1  # Convertir en 0-based
    end_idx = total_files if END_INDEX == -1 else min(END_INDEX, total_files)

    print(f"📁 Traitement de {total_files} fichiers")
    print(f"🎯 Application des options aux fichiers {start_idx + 1} à {end_idx}")

    for idx, file in enumerate(files, 1):
        stem = file.stem
        ext = file.suffix
        serial = f"{idx:04d}"

        # Vérifier si ce fichier est dans la plage d'indices
        apply_options = start_idx < idx <= end_idx

        if SERIAL_REPLACE_ALL and apply_options:
            new_stem = serial
        else:
            new_stem = stem

            # Appliquer les options seulement si dans la plage
            if apply_options:
                # Séparer le nom en parties à modifier et parties à préserver
                preserved_first_part = ""
                preserved_last_part = ""
                modifiable_part = new_stem

                # Préserver les premiers caractères si demandé
                if PRESERVE_FIRST_N_CHARS > 0 and len(new_stem) > PRESERVE_FIRST_N_CHARS:
                    preserved_first_part = new_stem[:PRESERVE_FIRST_N_CHARS]
                    modifiable_part = new_stem[PRESERVE_FIRST_N_CHARS:]

                # Préserver les derniers caractères si demandé
                if PRESERVE_LAST_N_CHARS > 0 and len(modifiable_part) > PRESERVE_LAST_N_CHARS:
                    preserved_last_part = modifiable_part[-PRESERVE_LAST_N_CHARS:]
                    modifiable_part = modifiable_part[:-PRESERVE_LAST_N_CHARS]

                # Supprimer le motif wildcard partiel sur la partie modifiable
                if REMOVE_TEXT:
                    modifiable_part = remove_wildcard_fragment_from_stem(modifiable_part, REMOVE_TEXT)

                # Enlever les tirets et underscores sur la partie modifiable
                if REMOVE_DASHES:
                    modifiable_part = modifiable_part.replace("-", "")
                if REMOVE_UNDERSCORES:
                    modifiable_part = modifiable_part.replace("_", "")

                # Reconstituer le nom complet
                new_stem = preserved_first_part + modifiable_part + preserved_last_part

                # Ajouter préfixe/suffixe
                if ADD_PREFIX:
                    new_stem = ADD_PREFIX + new_stem
                if ADD_SUFFIX:
                    new_stem = new_stem + ADD_SUFFIX

                # Sérialisation optionnelle
                if ADD_SERIAL_PREFIX:
                    new_stem = f"{serial}_{new_stem}"
                if ADD_SERIAL_SUFFIX:
                    new_stem = f"{new_stem}_{serial}"

        new_name = f"{new_stem}{ext}"
        new_path = folder / new_name

        # Afficher le changement
        if apply_options and new_name != file.name:
            print(f"  {idx:04d}: {file.name} → {new_name}")
        elif not apply_options:
            print(f"  {idx:04d}: {file.name} (non modifié)")

        os.rename(file, new_path)

    print(f"✅ {len(files)} fichiers traités dans : {folder_path}")
    print(f"📊 Options appliquées aux fichiers {start_idx + 1} à {end_idx}")

# === Lancer ===
if __name__ == "__main__":
    rename_files(FOLDER_PATH)
